import React, { useState, useRef, useContext, useEffect } from "react";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
// import { SocketContext } from "../../../helpers/context/socket.js";
import { setApiMessage } from "../../../helpers/context/toaster";
import { liveSocket } from "../../../helpers/context/socket";
import WebRTCViewerGenerator from "./WebRTCViewerGenerator";

const LiveStreamApp = () => {
  const socket = liveSocket;
  const [roomId, setRoomId] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [stream, setStream] = useState(null);
  const [livedata, setLivedata] = useState([]);
  const [isViewingStream, setIsViewingStream] = useState(false);
  const [currentViewingUserId, setCurrentViewingUserId] = useState(null);
  const isViewingStreamRef = useRef(false);
  const currentViewingUserIdRef = useRef(null);
  const [remoteStreams, setRemoteStreams] = useState(new Set());
  const [viewerCount, setViewerCount] = useState(0);
  const [comments, setComments] = useState([]);
  const [hearts, setHearts] = useState(0);
  const [commentInput, setCommentInput] = useState("");
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const peerConnectionsRef = useRef({});
  const pendingIceCandidatesRef = useRef({});
  const remoteVideosRef = useRef({});
  const localVideoRef = useRef(null);
  const localStreamRef = useRef(null);
  const commentsContainerRef = useRef(null);
  const [remoteStream, setRemoteStream] = useState(null);

  // Camera and microphone control states
  const [isMuted, setIsMuted] = useState(false);
  const [isCameraOff, setIsCameraOff] = useState(false);

  // WebRTC viewer generator state
  const [showWebRTCViewer, setShowWebRTCViewer] = useState(false);

  // Permission error state to prevent flickering
  const [hasPermissionError, setHasPermissionError] = useState(false);

  const userId = localStorage.getItem("UserId");

  // Get username from userData (stored as JSON)
  const getUserName = () => {
    try {
      const userData = JSON.parse(localStorage.getItem("userData"));
      return (
        userData?.name ||
        userData?.username ||
        localStorage.getItem("username") ||
        localStorage.getItem("name") ||
        localStorage.getItem("userName") ||
        "Anonymous"
      );
    } catch (error) {
      console.error("Error parsing userData:", error);
      return (
        localStorage.getItem("username") ||
        localStorage.getItem("name") ||
        localStorage.getItem("userName") ||
        "Anonymous"
      );
    }
  };

  const username = getUserName();

  // Debug log to check username
  // console.log("[LiveStream] Retrieved username:", username);
  // console.log("[LiveStream] UserData from localStorage:", localStorage.getItem("userData"));

  // Function to handle video orientation
  const applyVideoOrientation = (videoElement, isLocalStream = false) => {
    if (isLocalStream && isStreaming && !isViewingStream) {
      // For broadcaster's local video, flip horizontally to show mirror effect
      videoElement.style.transform = "scaleX(-1)";
    } else {
      // For remote videos or when viewing, keep normal orientation
      videoElement.style.transform = "none";
    }
  };

  // Toggle microphone function
  const toggleMute = () => {
    if (localStreamRef.current) {
      const newMutedState = !isMuted;
      setIsMuted(newMutedState);

      // Enable/disable audio tracks
      localStreamRef.current.getAudioTracks().forEach((track) => {
        track.enabled = !newMutedState;
        console.log(
          `[toggleMute] Audio track ${track.id} enabled: ${!newMutedState}`
        );
      });

      // Notify all peer connections about track state change
      Object.values(peerConnectionsRef.current).forEach((pc) => {
        if (pc && pc.connectionState === "connected") {
          console.log(
            `[toggleMute] Notifying peer connection about audio track state change`
          );
        }
      });

      console.log(
        `[toggleMute] Microphone ${newMutedState ? "muted" : "unmuted"}`
      );
    }
  };

  // Toggle camera function
  const toggleCamera = () => {
    if (localStreamRef.current) {
      const newCameraOffState = !isCameraOff;
      setIsCameraOff(newCameraOffState);

      // Enable/disable video tracks
      localStreamRef.current.getVideoTracks().forEach((track) => {
        track.enabled = !newCameraOffState;
        console.log(
          `[toggleCamera] Video track ${
            track.id
          } enabled: ${!newCameraOffState}`
        );
      });

      // Notify all peer connections about track state change
      Object.values(peerConnectionsRef.current).forEach((pc) => {
        if (pc && pc.connectionState === "connected") {
          console.log(
            `[toggleCamera] Notifying peer connection about video track state change`
          );
        }
      });

      // Emit camera status to server if broadcaster
      if (isStreaming && !isViewingStream && roomId && socket) {
        socket.emit("update_camera_status", {
          room: roomId,
          is_camera_on: newCameraOffState, // true if camera is OFF (to match Dart logic)
        });
        console.log(
          `[toggleCamera] Emitted update_camera_status:`,
          newCameraOffState
        );
      }

      console.log(
        `[toggleCamera] Camera ${
          newCameraOffState ? "turned off" : "turned on"
        }`
      );
    }
  };

  const iceConfiguration = {
    iceServers: [
      { urls: "stun:stun.l.google.com:19302" },
      { urls: "stun:stun1.l.google.com:19302" },
      { urls: "stun:stun2.l.google.com:19302" },
      { urls: "stun:stun3.l.google.com:19302" },
      { urls: "stun:stun4.l.google.com:19302" },
      {
        urls: "turn:openrelay.metered.ca:80",
        username: "openrelayproject",
        credential: "openrelayproject",
      },
      {
        urls: "turn:openrelay.metered.ca:443",
        username: "openrelayproject",
        credential: "openrelayproject",
      },
      {
        urls: "turn:openrelay.metered.ca:443?transport=tcp",
        username: "openrelayproject",
        credential: "openrelayproject",
      },
    ],
    iceCandidatePoolSize: 10,
  };

  // Function to send comment
  const sendComment = () => {
    if (!commentInput.trim() || !socket || !roomId) return;

    const comment = commentInput.trim();
    console.log("[sendComment] Sending comment:", comment);

    socket.emit("send_comment", {
      room: roomId,
      comment: comment,
      userId: userId,
      username: username,
      is_animated: false,
    });

    setCommentInput("");
    // Force scroll to bottom when user sends a comment
    setTimeout(() => {
      scrollToBottom();
    }, 50);
  };

  // Function to send emoji comment
  const sendEmojiComment = (emoji) => {
    if (!socket || !roomId) return;

    console.log("[sendEmojiComment] Sending emoji:", emoji);

    socket.emit("send_comment", {
      room: roomId,
      comment: emoji,
      userId: userId,
      username: username,
      is_animated: true,
    });

    // Force scroll to bottom when user sends an emoji
    setTimeout(() => {
      scrollToBottom();
    }, 50);
  };

  // Function to scroll to bottom of comments
  const scrollToBottom = () => {
    if (commentsContainerRef.current) {
      // Use smooth scrolling for better UX
      commentsContainerRef.current.scrollTo({
        top: commentsContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  // Auto-scroll to bottom when comments change
  useEffect(() => {
    // Add a small delay to ensure DOM is updated
    const timeoutId = setTimeout(() => {
      scrollToBottom();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [comments]);

  // Handle scroll events to show/hide scroll-to-bottom button
  const handleScroll = () => {
    if (commentsContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        commentsContainerRef.current;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 50;
      setShowScrollToBottom(!isNearBottom);
    }
  };

  const fetchLiveUsers = async () => {
    try {
      setIsLoading(true);
      const response = await apiInstance.get(URL.LIVE_USER);
      setLivedata(response.data.data);
      setIsLoading(false);
    } catch (error) {
      console.log("Error fetching live users:", error);
      setApiMessage("Failed to load live users");
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLiveUsers();
  }, []);

  // Function to handle WebRTC reconnection
  const handleWebRTCReconnection = async (userId) => {
    console.log(
      `[handleWebRTCReconnection] Attempting to reconnect with ${userId}`
    );

    // Clean up existing connection
    if (peerConnectionsRef.current[userId]) {
      peerConnectionsRef.current[userId].close();
      delete peerConnectionsRef.current[userId];
    }

    // Clear pending ICE candidates
    if (pendingIceCandidatesRef.current[userId]) {
      pendingIceCandidatesRef.current[userId] = [];
    }

    // Create new peer connection
    const pc = createPeerConnection(userId);

    // If we're the broadcaster, create a new offer
    if (isStreaming && !isViewingStream) {
      try {
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);

        socket.emit("offer", {
          offer: {
            type: offer.type,
            sdp: offer.sdp,
          },
          userId: userId,
          room: roomId,
        });

        console.log(`[handleWebRTCReconnection] Sent new offer to ${userId}`);
      } catch (error) {
        console.error(
          `[handleWebRTCReconnection] Error creating new offer:`,
          error
        );
      }
    }
  };

  const setStatus = (message, isError = false) => {
    setApiMessage(message);
    if (isError) {
      console.error(message);
    }
    if (!isError && message) {
      setTimeout(() => {
        setApiMessage("");
      }, 3000);
    }
  };

  const createPeerConnection = (userId) => {
    if (peerConnectionsRef.current[userId]) {
      console.log("Peer connection already exists for:", userId);
      return peerConnectionsRef.current[userId];
    }

    const pc = new RTCPeerConnection(iceConfiguration);

    pc.onicecandidate = (event) => {
      if (event.candidate) {
        // Match mobile app's ICE candidate format
        socket.emit("ice_candidate", {
          candidate: {
            candidate: event.candidate.candidate,
            sdpMid: event.candidate.sdpMid,
            sdpMLineIndex: event.candidate.sdpMLineIndex,
          },
          userId: userId,
          room: roomId,
        });
      }
    };

    pc.oniceconnectionstatechange = () => {
      console.log(
        `[PeerConnection] ICE state for ${userId}:`,
        pc.iceConnectionState
      );

      // Handle connection state changes
      if (pc.iceConnectionState === "connected") {
        console.log(`[PeerConnection] Successfully connected to ${userId}`);
      } else if (pc.iceConnectionState === "failed") {
        console.error(`[PeerConnection] Connection failed for ${userId}`);
        setStatus(`Connection failed for ${userId}`, true);
      } else if (pc.iceConnectionState === "disconnected") {
        console.warn(`[PeerConnection] Connection disconnected for ${userId}`);
      }
    };

    pc.onconnectionstatechange = () => {
      console.log(
        `[PeerConnection] Connection state for ${userId}:`,
        pc.connectionState
      );

      if (pc.connectionState === "connected") {
        console.log(
          `[PeerConnection] Peer connection established with ${userId}`
        );
      } else if (pc.connectionState === "failed") {
        console.error(`[PeerConnection] Peer connection failed with ${userId}`);
        setStatus(`Peer connection failed with ${userId}`, true);

        // Attempt reconnection after a delay
        setTimeout(() => {
          handleWebRTCReconnection(userId);
        }, 2000);
      } else if (pc.connectionState === "disconnected") {
        console.warn(
          `[PeerConnection] Peer connection disconnected with ${userId}`
        );

        // Attempt reconnection after a delay
        setTimeout(() => {
          handleWebRTCReconnection(userId);
        }, 1000);
      }
    };

    pc.onsignalingstatechange = () => {
      console.log(
        `[PeerConnection] Signaling state for ${userId}:`,
        pc.signalingState
      );
    };

    pc.ontrack = (event) => {
      console.log(
        `[ontrack] Remote track received (userId: ${userId}, isViewingStream: ${isViewingStream}, isStreaming: ${isStreaming})`,
        event.streams[0]
      );

      if (event.streams && event.streams.length > 0) {
        const streamId = event.streams[0].id;
        console.log(
          `[ontrack] Stream ID: ${streamId}, Track kind: ${event.track.kind}`
        );

        if (!remoteStreams.has(streamId)) {
          setRemoteStreams((prev) => new Set([...prev, streamId]));

          // For viewers, set the remote stream for playback
          if (isViewingStream) {
            console.log(
              `[ontrack][VIEWER] Setting remoteStream for userId: ${userId}`
            );
            setRemoteStream(event.streams[0]);

            // Ensure the video element is properly set up
            if (localVideoRef.current) {
              localVideoRef.current.srcObject = event.streams[0];
              localVideoRef.current.play().catch((e) => {
                console.error("[ontrack] Error playing remote video:", e);
              });
            }
          } else {
            console.log(
              `[ontrack][BROADCASTER] Received remote track from viewer userId: ${userId}`
            );
          }
        } else {
          console.log(`[ontrack] Stream ${streamId} already exists, skipping`);
        }
      } else {
        console.warn("[ontrack] No streams in track event");
      }
    };

    // Only add local stream tracks if this is the broadcaster (room creator)
    if (localStreamRef.current && isStreaming && !isViewingStream) {
      const tracks = localStreamRef.current.getTracks();
      console.log("[createPeerConnection] Tracks to add:", tracks);
      tracks.forEach((track) => {
        pc.addTrack(track, localStreamRef.current);
        console.log(
          `[createPeerConnection] Broadcaster adding track:`,
          track.kind,
          track.id
        );
      });
    }

    peerConnectionsRef.current[userId] = pc;
    console.log(
      "[createPeerConnection] Created new peer connection for:",
      userId
    );
    return pc;
  };

  const addRemoteVideo = (userId, streamId, videoElement) => {
    if (!remoteVideosRef.current[userId]) {
      remoteVideosRef.current[userId] = {};
    }
    remoteVideosRef.current[userId][streamId] = videoElement;
    setViewerCount(Object.keys(remoteVideosRef.current).length);
  };

  const removeRemoteVideo = (userId, streamId) => {
    if (
      remoteVideosRef.current[userId] &&
      remoteVideosRef.current[userId][streamId]
    ) {
      delete remoteVideosRef.current[userId][streamId];
    }
    if (Object.keys(remoteVideosRef.current[userId] || {}).length === 0) {
      delete remoteVideosRef.current[userId];
    }
    setViewerCount(Object.keys(remoteVideosRef.current).length);
  };

  const stopExistingStream = () => {
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach((track) => track.stop());
      localStreamRef.current = null;
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = null;
      }
    }

    Object.entries(remoteVideosRef.current).forEach(([userId, streams]) => {
      Object.entries(streams).forEach(([streamId, videoElement]) => {
        if (videoElement) {
          const video = videoElement.querySelector("video");
          if (video) {
            video.srcObject = null;
          }
          videoElement.remove();
          removeRemoteVideo(userId, streamId);
        }
      });
    });

    Object.values(peerConnectionsRef.current).forEach((pc) => {
      if (pc) {
        pc.close();
      }
    });
    peerConnectionsRef.current = {};
    setRemoteStreams(new Set());
    setIsStreaming(false);
    setIsViewingStream(false);
    setStream(null);
    setComments([]);
    setViewerCount(0);
    setRemoteStream(null);

    // Reset camera and microphone states
    setIsMuted(false);
    setIsCameraOff(false);

    // Reset permission error state
    setHasPermissionError(false);
  };

  const cleanupUser = (userId) => {
    if (remoteVideosRef.current[userId]) {
      Object.entries(remoteVideosRef.current[userId]).forEach(
        ([streamId, videoElement]) => {
          if (videoElement) {
            const video = videoElement.querySelector("video");
            if (video) {
              video.srcObject = null;
            }
            videoElement.remove();
            removeRemoteVideo(userId, streamId);
          }
        }
      );
    }

    if (peerConnectionsRef.current[userId]) {
      peerConnectionsRef.current[userId].close();
      delete peerConnectionsRef.current[userId];
    }

    // Request updated user count after cleanup
    if (socket && roomId) {
      setTimeout(() => {
        socket.emit("current_users_number", { room: roomId });
      }, 1000);
    }
  };

  useEffect(() => {
    if (stream && localVideoRef.current && isStreaming && !isViewingStream) {
      const videoElement = localVideoRef.current;
      videoElement.srcObject = stream;

      const handleLoadedMetadata = () => {
        if (videoElement && !videoElement.paused) {
          videoElement.play().catch((e) => {
            console.error("Error playing video:", e);
            setStatus(`Error playing video: ${e.message}`, true);
          });
        }
        // Apply orientation after metadata is loaded
        applyVideoOrientation(videoElement, true);
      };

      videoElement.onloadedmetadata = handleLoadedMetadata;

      return () => {
        if (videoElement) {
          videoElement.onloadedmetadata = null;
          videoElement.srcObject = null;
        }
      };
    }
  }, [stream, isStreaming, isViewingStream]);

  // Effect for viewers to play remote stream
  useEffect(() => {
    if (isViewingStream && localVideoRef.current && remoteStream) {
      console.log("[VideoEffect][VIEWER] Setting remote stream for viewer");
      localVideoRef.current.srcObject = remoteStream;

      const handleLoadedMetadata = () => {
        console.log("[VideoEffect][VIEWER] Remote stream metadata loaded");
        if (localVideoRef.current && !localVideoRef.current.paused) {
          localVideoRef.current.play().catch((e) => {
            console.error("Error playing remote video:", e);
            setStatus(`Error playing remote video: ${e.message}`, true);
          });
        }
        // Apply orientation for viewer (should be normal, not flipped)
        applyVideoOrientation(localVideoRef.current, false);
      };

      const handleCanPlay = () => {
        console.log("[VideoEffect][VIEWER] Remote stream can play");
      };

      const handleError = (e) => {
        console.error("[VideoEffect][VIEWER] Remote stream error:", e);
      };

      localVideoRef.current.onloadedmetadata = handleLoadedMetadata;
      localVideoRef.current.oncanplay = handleCanPlay;
      localVideoRef.current.onerror = handleError;

      return () => {
        if (localVideoRef.current) {
          localVideoRef.current.onloadedmetadata = null;
          localVideoRef.current.oncanplay = null;
          localVideoRef.current.onerror = null;
          localVideoRef.current.srcObject = null;
        }
      };
    }
  }, [isViewingStream, remoteStream]);

  // Effect to handle video orientation when streaming state changes
  useEffect(() => {
    if (localVideoRef.current) {
      applyVideoOrientation(localVideoRef.current, !isViewingStream);
    }
  }, [isStreaming, isViewingStream]);

  // Effect to periodically request user count when streaming or viewing
  useEffect(() => {
    if (!socket || !roomId || (!isStreaming && !isViewingStream)) return;

    // Request user count immediately
    socket.emit("current_users_number", { room: roomId });

    // Set up periodic requests every 5 seconds
    const intervalId = setInterval(() => {
      if (socket && roomId) {
        socket.emit("current_users_number", { room: roomId });
      }
    }, 5000);

    return () => {
      clearInterval(intervalId);
    };
  }, [socket, roomId, isStreaming, isViewingStream]);

  useEffect(() => {
    console.log("[useEffect] Setting up socket listeners");
    if (!liveSocket) {
      console.error("Socket not initialized");
      setStatus("Socket connection not established", true);
      return;
    }

    const handleConnect = () => {
      console.log("[socket] handleConnect:", socket.id);
      setIsConnected(true);
      setApiMessage("");
    };

    const handleDisconnect = () => {
      console.log("[socket] handleDisconnect");
      setIsConnected(false);
      setStatus("Disconnected from server", true);

      // If viewing a stream and socket disconnects, treat it as stream ended
      if (isViewingStream) {
        setApiMessage(
          "warning",
          "Connection lost. The live stream may have ended."
        );

        // Clean up and return to main screen
        stopExistingStream();
        setRoomId("");
        setCurrentViewingUserId(null);
        setIsViewingStream(false);
        isViewingStreamRef.current = false;
        setComments([]);
        setCommentInput("");
        setRemoteStream(null);
      } else {
        stopExistingStream();
      }
    };

    const handleConnectError = (error) => {
      console.error("[socket] handleConnectError:", error);
      setIsConnected(false);
      setStatus(`Connection error: ${error.message || "Unknown error"}`, true);

      // If viewing a stream and connection error occurs, treat it as stream ended
      if (isViewingStream) {
        setApiMessage(
          "error",
          "Connection error. The live stream may have ended."
        );

        // Clean up and return to main screen
        stopExistingStream();
        setRoomId("");
        setCurrentViewingUserId(null);
        setIsViewingStream(false);
        isViewingStreamRef.current = false;
        setComments([]);
        setCommentInput("");
        setRemoteStream(null);
      }
    };

    const handleRoomCreated = (data) => {
      console.log("[socket] handleRoomCreated:", data);
      setStatus(`Room '${data.room}' created successfully!`);
    };

    const handleUserJoined = async (data) => {
      console.log(
        "[socket] handleUserJoined:",
        data,
        "isStreaming:",
        isStreaming,
        "isViewingStream:",
        isViewingStream,
        "userId:",
        userId
      );
      // Only broadcaster should handle this
      if (data.room === roomId && isStreaming && !isViewingStream) {
        console.log(
          "[handleUserJoined] Broadcaster creating offer for viewer:",
          data.userId
        );
        const pc = createPeerConnection(data.userId); // data.userId is the viewer
        try {
          const offer = await pc.createOffer();
          await pc.setLocalDescription(offer);
          console.log(
            "[handleUserJoined] Broadcaster created offer for viewer",
            data.userId,
            offer
          );

          // Match mobile app's offer format
          socket.emit("offer", {
            offer: {
              type: offer.type,
              sdp: offer.sdp,
            },
            userId: data.userId, // send to viewer
            room: roomId,
          });
        } catch (error) {
          console.error("[handleUserJoined] Error creating offer:", error);
          setStatus("Failed to establish connection with viewer", true);
        }
      }

      // Request updated user count when someone joins
      if (socket && roomId) {
        setTimeout(() => {
          socket.emit("current_users_number", { room: roomId });
        }, 500);
      }
    };

    const handleOffer = async (data) => {
      console.log(
        "[socket] handleOffer:",
        data,
        "isViewingStream:",
        isViewingStream,
        "currentViewingUserId:",
        currentViewingUserId
      );
      // Only viewer should handle this
      if (data.room === roomId && isViewingStreamRef.current) {
        // data.userId is the broadcaster's userId
        const broadcasterId = data.userId;
        console.log(
          "[handleOffer] Received offer from broadcaster:",
          broadcasterId,
          data.offer
        );
        const pc = createPeerConnection(broadcasterId);

        try {
          console.log("[handleOffer] Before setRemoteDescription");
          // Handle both formats: direct offer object or nested offer object
          const offerToSet = data.offer.sdp ? data.offer : data.offer.offer;
          await pc.setRemoteDescription(offerToSet);
          console.log("[handleOffer] After setRemoteDescription");

          // Process pending ICE candidates after setting remote description
          const pending = pendingIceCandidatesRef.current[broadcasterId];
          if (pending && pending.length > 0) {
            for (const candidate of pending) {
              const iceCandidate = new RTCIceCandidate(candidate);
              await pc.addIceCandidate(iceCandidate);
            }
            pendingIceCandidatesRef.current[broadcasterId] = [];
          }

          console.log("[handleOffer] Before createAnswer");
          const answer = await pc.createAnswer();
          console.log("[handleOffer] After createAnswer", answer);
          console.log("[handleOffer] Before setLocalDescription");
          await pc.setLocalDescription(answer);
          console.log(
            "[handleOffer] setLocalDescription(answer) complete",
            answer
          );
          console.log(
            "[handleOffer] Viewer created answer for broadcaster",
            broadcasterId,
            answer
          );

          // Match mobile app's answer format
          socket.emit("answer", {
            answer: {
              type: answer.type,
              sdp: answer.sdp,
            },
            userId: broadcasterId, // send to broadcaster
            room: roomId,
          });
        } catch (error) {
          console.error(
            "[handleOffer] Error after setRemoteDescription or setLocalDescription or createAnswer:",
            error
          );
          setStatus("Failed to establish connection with broadcaster", true);
        }
      }
    };

    const handleAnswer = async (data) => {
      console.log(
        "[socket] handleAnswer:",
        data,
        "isStreaming:",
        isStreaming,
        "isViewingStream:",
        isViewingStream,
        "userId:",
        userId
      );
      if (data.room === roomId && isStreaming && !isViewingStream) {
        try {
          const pc = peerConnectionsRef.current[data.userId];
          if (pc) {
            // Only set remote description if in the correct state
            if (pc.signalingState === "have-local-offer") {
              // Handle both formats: direct answer object or nested answer object
              const answerToSet = data.answer.sdp
                ? data.answer
                : data.answer.answer;
              await pc.setRemoteDescription(answerToSet);
              console.log(
                "[handleAnswer] Broadcaster set remote description from viewer",
                data.userId,
                answerToSet
              );
              // Process pending ICE candidates after setting remote description
              const pending = pendingIceCandidatesRef.current[data.userId];
              if (pending && pending.length > 0) {
                for (const candidate of pending) {
                  const iceCandidate = new RTCIceCandidate(candidate);
                  await pc.addIceCandidate(iceCandidate);
                }
                pendingIceCandidatesRef.current[data.userId] = [];
              }
            } else {
              console.warn(
                `[handleAnswer] Skipping setRemoteDescription: signalingState is ${pc.signalingState}`
              );
            }
          }
        } catch (error) {
          console.error("[handleAnswer] Error handling answer:", error);
        }
      }
    };

    const handleIceCandidate = async (data) => {
      console.log(
        "[socket] handleIceCandidate:",
        data,
        "isStreaming:",
        isStreaming,
        "isViewingStream:",
        isViewingStream,
        "userId:",
        userId,
        "currentViewingUserId:",
        currentViewingUserId
      );
      if (data.room === roomId) {
        try {
          const pc = peerConnectionsRef.current[data.userId];
          if (pc && pc.remoteDescription) {
            // Handle mobile app's ICE candidate format
            const candidateData = data.candidate.candidate
              ? data.candidate
              : data.candidate;
            const iceCandidate = new RTCIceCandidate(candidateData);
            await pc.addIceCandidate(iceCandidate);
          } else if (pc) {
            if (!pendingIceCandidatesRef.current[data.userId]) {
              pendingIceCandidatesRef.current[data.userId] = [];
            }
            // Store the candidate in the same format it was received
            pendingIceCandidatesRef.current[data.userId].push(data.candidate);
          }
        } catch (error) {
          console.error(
            "[handleIceCandidate] Error adding ICE candidate:",
            error
          );
        }
      }
    };

    const handleUserLeft = (data) => {
      console.log("[socket] handleUserLeft:", data);
      if (data.userId) {
        cleanupUser(data.userId);
      }

      // Request updated user count when someone leaves
      if (socket && roomId) {
        setTimeout(() => {
          socket.emit("current_users_number", { room: roomId });
        }, 500);
      }
    };

    const handleStreamEnded = (data) => {
      console.log("[socket] handleStreamEnded:", data);
      if (isViewingStream && data.userId === currentViewingUserId) {
        setStatus("The live stream has ended", true);
        stopExistingStream();
        setRoomId("");
        setCurrentViewingUserId(null);
        setApiMessage("info", "The live stream has ended");
      }
    };

    const handleRoomClosed = (data) => {
      console.log("[socket] handleRoomClosed:", data);
      if (data.room === roomId && isViewingStream) {
        // Show toast notification
        setApiMessage("warning", "The live has ended by the host.");

        // Clean up and return to main screen
        stopExistingStream();
        setRoomId("");
        setCurrentViewingUserId(null);
        setIsViewingStream(false);
        isViewingStreamRef.current = false;
        setComments([]);
        setCommentInput("");
        setRemoteStream(null);

        // Show status message
        setStatus("The live stream has ended by the host", true);
      }
    };

    const handleCommentList = (data) => {
      console.log("[socket] handleCommentList:", data);
      if (data.room === roomId) {
        setComments([]);
        if (data.comments && Array.isArray(data.comments)) {
          const formattedComments = data.comments.map((comment) => ({
            message: comment.comment || comment.message,
            username: comment.username,
            timestamp: comment.timestamp || new Date(),
            isOwnMessage: false, // Always show on left side
          }));
          setComments(formattedComments);
          // Scroll to bottom when comments are loaded
          setTimeout(() => {
            scrollToBottom();
          }, 200);
        }
      }
    };

    const handleNewComment = (data) => {
      console.log("[socket] handleNewComment:", data);
      if (data.room === roomId) {
        setComments((prev) => [
          ...prev,
          {
            message: data.comment,
            username: data.username,
            timestamp: data.timestamp || new Date(),
            isOwnMessage: false, // Always show on left side
          },
        ]);
      }
    };

    const handleCurrentUsersNumber = (data) => {
      console.log("[socket] handleCurrentUsersNumber:", data);
      if (data.room === roomId) {
        // Subtract 2 to account for broadcaster and adjust for accurate viewer count
        const viewerCount = data.count > 0 ? data.count - 2 : 0;
        setViewerCount(Math.max(0, viewerCount));
      }
    };

    const handleCameraStatus = (data) => {
      console.log("[socket] camera_status:", data);
      if (data.room === roomId) {
        setIsCameraOff(data.is_camera_on); // true means camera is OFF (to match Dart logic)
      }
    };

    if (socket.connected) {
      setIsConnected(true);
    }

    socket.on("connect", handleConnect);
    socket.on("disconnect", handleDisconnect);
    socket.on("connect_error", handleConnectError);
    socket.on("room_created", handleRoomCreated);
    socket.on("user_joined", handleUserJoined);
    socket.on("offer", handleOffer);
    socket.on("answer", handleAnswer);
    socket.on("ice_candidate", handleIceCandidate);
    socket.on("user_left", handleUserLeft);
    socket.on("stream_ended", handleStreamEnded);
    socket.on("room_closed", handleRoomClosed);
    socket.on("comment_list", handleCommentList);
    socket.on("new_comment", handleNewComment);
    socket.on("current_users_number", handleCurrentUsersNumber);
    socket.on("camera_status", handleCameraStatus);

    return () => {
      socket.off("connect", handleConnect);
      socket.off("disconnect", handleDisconnect);
      socket.off("connect_error", handleConnectError);
      socket.off("room_created", handleRoomCreated);
      socket.off("user_joined", handleUserJoined);
      socket.off("offer", handleOffer);
      socket.off("answer", handleAnswer);
      socket.off("ice_candidate", handleIceCandidate);
      socket.off("user_left", handleUserLeft);
      socket.off("stream_ended", handleStreamEnded);
      socket.off("room_closed", handleRoomClosed);
      socket.off("comment_list", handleCommentList);
      socket.off("new_comment", handleNewComment);
      socket.off("current_users_number", handleCurrentUsersNumber);
      socket.off("camera_status", handleCameraStatus);
    };
  }, [
    socket,
    userId,
    currentViewingUserId,
    isStreaming,
    isViewingStream,
    roomId,
  ]);

  const createRoom = async () => {
    console.log("[createRoom] userId:", userId, "roomId:", `liv-${userId}`);
    if (!socket || !isConnected) {
      setStatus("Socket connection not established", true);
      return;
    }

    if (!userId) {
      setStatus("User ID not found. Please login first.", true);
      return;
    }

    // Check if getUserMedia is supported
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setStatus(
        "Your browser doesn't support camera and microphone access",
        true
      );
      return;
    }

    // Reset permission error state when trying again
    setHasPermissionError(false);
    setIsLoading(true);
    setRoomId(`liv-${userId}`);
    stopExistingStream();
    setComments([]); // Clear previous comments
    setCommentInput(""); // Clear comment input

    // Reset camera and microphone states for new stream
    setIsMuted(false);
    setIsCameraOff(false);

    try {
      const constraints = {
        video: {
          width: { min: 320, ideal: 640, max: 1280 },
          height: { min: 240, ideal: 480, max: 720 },
          facingMode: "user",
          frameRate: { ideal: 30, max: 30 },
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: { ideal: 48000 },
          channelCount: { ideal: 2 },
        },
      };

      const mediaStream = await navigator.mediaDevices.getUserMedia(
        constraints
      );
      localStreamRef.current = mediaStream;
      setStream(mediaStream);
      setIsStreaming(true);

      console.log("[createRoom] emitting create_room", `liv-${userId}`);
      socket.emit("create_room", { room: `liv-${userId}` });
      setApiMessage("success", "Live stream Started successfully!");

      // Emit join_room for the creator as well, matching Dart flow
      console.log("[createRoom] emitting join_room", `liv-${userId}`);
      socket.emit("join_room", { room: `liv-${userId}` });

      // Request current user count for the room
      console.log(
        "[createRoom] requesting current_users_number",
        `liv-${userId}`
      );
      socket.emit("current_users_number", { room: `liv-${userId}` });

      try {
        await apiInstance.post(URL.START_LIVE, { userId });
      } catch (error) {
        console.error("Failed to update live status:", error);
      }

      setStatus("You are now live streaming!");
      setIsLoading(false);
    } catch (error) {
      console.error("Error accessing media devices:", error);

      // Check if it's a permission error
      if (
        error.name === "NotAllowedError" ||
        error.name === "PermissionDeniedError"
      ) {
        setHasPermissionError(true);
        setStatus(
          "Camera and microphone permissions are required. Please allow access and try again.",
          true
        );
      } else if (error.name === "NotFoundError") {
        setStatus(
          "No camera or microphone found. Please connect a device and try again.",
          true
        );
      } else if (error.name === "NotReadableError") {
        setStatus(
          "Camera or microphone is already in use by another application.",
          true
        );
      } else if (error.name === "OverconstrainedError") {
        setStatus(
          "Camera or microphone doesn't meet the required specifications.",
          true
        );
      } else {
        setStatus(
          "Error accessing camera and microphone! " + error.message,
          true
        );
      }

      setIsLoading(false);
    }
  };

  const joinLiveStream = async (targetUserId) => {
    console.log("[joinLiveStream] called with:", targetUserId);
    console.log(
      "[joinLiveStream] userId:",
      userId,
      "currentViewingUserId:",
      currentViewingUserId,
      "roomId:",
      `liv-${targetUserId}`
    );
    if (!socket || !isConnected) {
      setStatus("Socket connection not established", true);
      return;
    }

    if (!userId) {
      setStatus("User ID not found. Please login first.", true);
      return;
    }

    if (targetUserId === userId) {
      setStatus("You cannot join your own live stream", true);
      return;
    }

    setIsLoading(true);
    stopExistingStream();
    setCurrentViewingUserId(targetUserId);
    currentViewingUserIdRef.current = targetUserId;
    setIsViewingStream(true);
    isViewingStreamRef.current = true;
    setRoomId(`liv-${targetUserId}`);
    setComments([]);
    setCommentInput("");

    try {
      // Viewers don't need local stream - they only receive remote stream
      setStream(null);
      setRemoteStream(null);

      console.log(
        "[joinLiveStream] emitting check_room",
        `liv-${targetUserId}`
      );
      socket.emit("check_room", { room: `liv-${targetUserId}` });
      console.log("[joinLiveStream] emitting join_room", `liv-${targetUserId}`);
      socket.emit("join_room", { room: `liv-${targetUserId}` });
      console.log(
        "[joinLiveStream] emitting get_comment_list",
        `liv-${targetUserId}`
      );
      socket.emit("get_comment_list", { room: `liv-${targetUserId}` });

      // Request current user count for the room
      console.log(
        "[joinLiveStream] requesting current_users_number",
        `liv-${targetUserId}`
      );
      socket.emit("current_users_number", { room: `liv-${targetUserId}` });

      setStatus(`Joined ${targetUserId}'s live stream`);
      setIsLoading(false);
    } catch (error) {
      console.error("Error joining room:", error);
      setStatus("Failed to join live stream. Please try again.", true);
      setIsLoading(false);
    }
  };

  const endLiveStream = async () => {
    if (socket && roomId) {
      if (isStreaming && !isViewingStream) {
        // Broadcaster ending the stream
        console.log("[endLiveStream] Broadcaster ending stream, room:", roomId);
        socket.emit("close_room", { room: roomId });
        setApiMessage("info", "Live stream ended successfully!");
        try {
          await apiInstance.post(URL.END_LIVE, { userId });
        } catch (error) {
          console.error("Failed to update live status:", error);
        }
      } else {
        // Viewer leaving the stream
        console.log("[endLiveStream] Viewer leaving stream, room:", roomId);
        socket.emit("leave_stream", { room: roomId });
      }
    }
    stopExistingStream();
    setRoomId("");
    setCurrentViewingUserId(null);
    setIsViewingStream(false);
    isViewingStreamRef.current = false;
    setComments([]);
    setCommentInput("");
    setRemoteStream(null);
    setViewerCount(0);
    setStatus(isStreaming ? "Live stream ended" : "Left the stream");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F9F9F9] via-[#F3EDE7] to-[#E0E0E0] font-[Ubuntu,sans-serif]">
      {/* Sticky Header */}
      <header className="sticky top-0 z-20 backdrop-blur-md px-3 sm:px-4 md:px-6 py-3 sm:py-4 flex items-center justify-between">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-extrabold text-[#563D39] tracking-tight">
          Live Streaming
        </h1>
        {/* Connection status indicator for mobile */}
        <div className="flex items-center gap-2 sm:hidden">
          <span
            className={`w-2 h-2 rounded-full ${
              isConnected ? "bg-green-500" : "bg-red-500"
            }`}
          ></span>
          <span className="text-xs text-[#563D39] font-medium">
            {isConnected ? "Online" : "Offline"}
          </span>
        </div>
      </header>

      <main className="max-w-[90rem] mx-auto flex flex-col lg:flex-row gap-4 sm:gap-6 lg:gap-8 py-4 sm:py-6 lg:py-8 px-2 sm:px-4 md:px-6">
        {/* Main Video/Card Area */}
        <section className="flex-1 w-full mx-auto order-2 lg:order-1">
          {/* Not streaming or viewing: Show start and live users */}
          {!isStreaming && !isViewingStream ? (
            <div className="flex flex-col gap-4 sm:gap-6 lg:gap-8">
              {/* Start Stream Card */}
              <div className="bg-gradient-to-br from-[#F9F9F9] to-[#E0E0E0] rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8 flex flex-col items-center text-center">
                <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-[#563D39] mb-2">
                  Start Your Live Stream
                </h2>
                <p className="text-sm sm:text-base text-[#674941] mb-4 sm:mb-6">
                  Share your moment with your followers in real-time. Go live
                  now!
                </p>
                <button
                  onClick={createRoom}
                  disabled={
                    !isConnected || isLoading || !userId || hasPermissionError
                  }
                  className={`flex items-center gap-2 px-4 sm:px-6 lg:px-8 py-2 sm:py-3 rounded-full text-sm sm:text-base lg:text-lg font-bold shadow-lg transition-all duration-200 ${
                    isConnected && !isLoading && userId && !hasPermissionError
                      ? "bg-gradient-to-r from-[#563D39] to-[#BC857D] hover:scale-105 text-white"
                      : "bg-gray-300 text-gray-400 cursor-not-allowed"
                  }`}
                >
                  {isLoading ? (
                    <svg
                      className="animate-spin h-4 w-4 sm:h-5 sm:w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                  ) : (
                    <>
                      <svg
                        className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M4 6v12a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2z"
                        />
                      </svg>
                      <span className="hidden sm:inline">Go Live</span>
                      <span className="sm:hidden">Live</span>
                    </>
                  )}
                </button>
                {!userId && (
                  <p className="text-xs sm:text-sm text-[#d84727] mt-2 sm:mt-3">
                    User ID not found. Please login first.
                  </p>
                )}
                {hasPermissionError && (
                  <div className="mt-2 sm:mt-3 p-2 sm:p-3 bg-[#FFF3F3] border border-[#d84727] rounded-lg w-full max-w-sm">
                    <p className="text-xs sm:text-sm text-[#d84727] mb-2">
                      Camera and microphone permissions are required to go live.
                    </p>
                    <button
                      onClick={() => {
                        setHasPermissionError(false);
                        setStatus("");
                      }}
                      className="text-xs sm:text-sm text-[#d84727] underline hover:no-underline"
                    >
                      Try again
                    </button>
                  </div>
                )}
              </div>

              {/* Live Users Grid */}
              <div className="bg-white rounded-xl sm:rounded-2xl shadow-lg p-3 sm:p-4 lg:p-6">
                <h2 className="text-base sm:text-lg md:text-xl font-bold text-[#563D39] mb-3 sm:mb-4 flex items-center gap-2">
                  <span className="w-2 h-2 sm:w-3 sm:h-3 bg-[#d84727] rounded-full animate-pulse"></span>
                  Live Users
                </h2>
                {isLoading ? (
                  <div className="flex justify-center items-center py-6 sm:py-8">
                    <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-[#674941]"></div>
                    <span className="ml-2 text-sm sm:text-base text-[#674941]">
                      Loading streams...
                    </span>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 sm:gap-4 lg:gap-6">
                    {livedata?.length > 0 ? (
                      livedata.map((user) => (
                        <div
                          key={user.user_id}
                          className="group bg-gradient-to-br from-[#F9F9F9] to-[#E0E0E0] rounded-lg sm:rounded-xl shadow hover:shadow-xl transition cursor-pointer flex flex-col items-center p-3 sm:p-4 relative min-h-[120px] sm:min-h-[140px]"
                        >
                          <div className="relative w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 mb-2">
                            <img
                              src={
                                user.userprofile
                                  ? `${URL.SOCKET_URL}${user.userprofile}`
                                  : "https://ui-avatars.com/api/?name=" +
                                    (user.username || "User") +
                                    "&background=674941&color=fff"
                              }
                              alt={user.username || "Unknown"}
                              className="w-full h-full object-cover rounded-full border-2 sm:border-4 border-white shadow"
                            />
                            <span className="absolute -bottom-1 -right-1 sm:bottom-0 sm:right-0 bg-gradient-to-r from-[#563D39] to-[#BC857D] text-white text-xs font-bold px-1 sm:px-2 py-0.5 rounded-full shadow text-[10px] sm:text-xs">
                              LIVE
                            </span>
                          </div>
                          <span className="font-semibold text-[#563D39] truncate w-full text-center text-xs sm:text-sm">
                            {user.username || "Unknown"}
                          </span>
                          <div className="flex gap-1 sm:gap-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <button
                              onClick={() => joinLiveStream(user.user_id)}
                              className="px-2 sm:px-3 lg:px-4 py-1 rounded-full bg-gradient-to-r from-[#563D39] to-[#BC857D] text-white text-xs font-bold shadow hover:scale-105 transition-all"
                            >
                              Join
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                const streamLink = `${window.location.origin}/u/liv-${user.user_id}`;
                                navigator.clipboard
                                  .writeText(streamLink)
                                  .then(() => {
                                    setApiMessage(
                                      "success",
                                      "Universal stream link copied to clipboard!"
                                    );
                                  })
                                  .catch(() => {
                                    setApiMessage(
                                      "error",
                                      "Failed to copy link"
                                    );
                                  });
                              }}
                              className="px-1 sm:px-2 py-1 rounded-full bg-[#674941] text-white text-xs font-bold shadow hover:scale-105 transition-all"
                              title="Copy universal stream link"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-3 w-3"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                                />
                              </svg>
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                const webrtcUrl = `${window.location.origin}/webrtc-viewer.html?stream=liv-${user.user_id}`;
                                navigator.clipboard
                                  .writeText(webrtcUrl)
                                  .then(() => {
                                    setApiMessage(
                                      "success",
                                      "WebRTC viewer link copied to clipboard!"
                                    );
                                  })
                                  .catch(() => {
                                    setApiMessage(
                                      "error",
                                      "Failed to copy WebRTC link"
                                    );
                                  });
                              }}
                              className="px-1 sm:px-2 py-1 rounded-full bg-[#563D39] text-white text-xs font-bold shadow hover:scale-105 transition-all"
                              title="Copy WebRTC viewer link"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-3 w-3"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 10l4.553-2.276A1 1 0 0010 8.618v6.764a1 1 0 001.447.894L5 14"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="col-span-full flex flex-col items-center py-8 sm:py-12">
                        <span className="text-[#674941] text-xs sm:text-sm md:text-base lg:text-lg text-center px-4">
                          No users are currently live streaming
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ) : (
            // Streaming or Viewing UI
            <div className="relative bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden flex flex-col">
              {/* Video Area */}
              <div className="relative aspect-video bg-black rounded-t-xl sm:rounded-t-2xl overflow-hidden flex items-center justify-center">
                {/* Show video only if camera is not off */}
                <video
                  ref={localVideoRef}
                  autoPlay
                  playsInline
                  muted={isStreaming && !isViewingStream}
                  className="w-full h-full object-contain rounded-t-xl sm:rounded-t-2xl shadow-lg"
                  style={{ display: isCameraOff ? "none" : "block" }}
                />
                {/* Show placeholder if camera is off */}
                {isCameraOff && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black text-white">
                    <span className="text-lg sm:text-xl font-bold">
                      Camera is Off
                    </span>
                  </div>
                )}
                {/* Show loading indicator for viewers waiting for stream */}
                {isViewingStream && !remoteStream && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-white text-center">
                      <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-white mx-auto mb-3 sm:mb-4"></div>
                      <p className="text-sm sm:text-base">
                        Connecting to stream...
                      </p>
                    </div>
                  </div>
                )}
                {isStreaming && !isViewingStream && (
                  <span className="absolute top-2 sm:top-4 left-2 sm:left-4 bg-gradient-to-r from-[#563D39] to-[#BC857D] text-white px-2 sm:px-4 py-1 rounded-full text-xs sm:text-base font-bold shadow-lg animate-pulse">
                    LIVE
                  </span>
                )}
                <div className="absolute top-2 sm:top-4 right-2 sm:right-4 flex gap-2 sm:gap-3 items-center">
                  <span className="flex items-center gap-1 bg-[#563D39]/80 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold shadow-lg backdrop-blur-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3 sm:h-4 sm:w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    <span className="hidden sm:inline">{viewerCount}</span>
                    <span className="sm:hidden">{viewerCount}</span>
                  </span>
                  {isStreaming && !isViewingStream && (
                    <button
                      onClick={() => {
                        const streamLink = `${window.location.origin}/u/${roomId}`;
                        navigator.clipboard
                          .writeText(streamLink)
                          .then(() => {
                            setApiMessage(
                              "success",
                              "Universal stream link copied to clipboard!"
                            );
                          })
                          .catch(() => {
                            setApiMessage("error", "Failed to copy link");
                          });
                      }}
                      className="bg-[#563D39]/80 text-white p-1.5 sm:p-2 rounded-full shadow-lg backdrop-blur-sm hover:scale-110 transition-all duration-200"
                      title="Copy universal stream link"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 sm:h-5 sm:w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                        />
                      </svg>
                    </button>
                  )}
                </div>

                {/* Camera and Microphone Controls - Only show for broadcaster */}
                {isStreaming && !isViewingStream && (
                  <div className="absolute top-12 sm:top-16 md:top-20 right-2 sm:right-4 flex flex-col gap-2 sm:gap-3 z-10">
                    {/* Microphone Toggle Button */}
                    <button
                      onClick={toggleMute}
                      className={`p-2 sm:p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110 min-w-[44px] min-h-[44px] flex items-center justify-center ${
                        isMuted
                          ? "bg-[#d84727] text-white"
                          : "bg-[#563D39]/80 text-white hover:bg-[#563D39]"
                      }`}
                      title={isMuted ? "Unmute" : "Mute"}
                    >
                      {isMuted ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"
                            clipRule="evenodd"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                          />
                        </svg>
                      )}
                    </button>

                    {/* Camera Toggle Button */}
                    <button
                      onClick={toggleCamera}
                      className={`p-2 sm:p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110 min-w-[44px] min-h-[44px] flex items-center justify-center ${
                        isCameraOff
                          ? "bg-[#d84727] text-white"
                          : "bg-[#563D39]/80 text-white hover:bg-[#563D39]"
                      }`}
                      title={isCameraOff ? "Turn on camera" : "Turn off camera"}
                    >
                      {isCameraOff ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 10l4.553-2.276A1 1 0 0010 8.618v6.764a1 1 0 001.447.894L5 14"
                          />
                        </svg>
                      )}
                    </button>
                  </div>
                )}
              </div>
              {/* End/Leave Button */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 px-3 sm:px-4 lg:px-6 py-3 bg-gradient-to-r from-[#F9F9F9] to-[#E0E0E0] border-b border-[#E0E0E0]">
                <span className="text-xs sm:text-sm text-[#563D39] font-medium">
                  {isStreaming && !isViewingStream ? (
                    <>
                      Streaming as:{" "}
                      <span className="font-bold">{username}</span>
                    </>
                  ) : (
                    "Viewing stream"
                  )}
                </span>
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
                  {isStreaming && !isViewingStream && (
                    <>
                      <button
                        onClick={() => {
                          const streamLink = `${window.location.origin}/u/${roomId}`;
                          navigator.clipboard
                            .writeText(streamLink)
                            .then(() => {
                              setApiMessage(
                                "success",
                                "Universal stream link copied to clipboard!"
                              );
                            })
                            .catch(() => {
                              setApiMessage("error", "Failed to copy link");
                            });
                        }}
                        className="px-3 sm:px-4 py-2 bg-gradient-to-r from-[#674941] to-[#BC857D] hover:scale-105 text-white rounded-full font-bold shadow transition flex items-center justify-center gap-2 text-xs sm:text-sm"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3 sm:h-4 sm:w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                          />
                        </svg>
                        <span className="hidden sm:inline">Universal Link</span>
                        <span className="sm:hidden">Universal</span>
                      </button>
                      <button
                        onClick={() => setShowWebRTCViewer(true)}
                        className="px-3 sm:px-4 py-2 bg-gradient-to-r from-[#563D39] to-[#674941] hover:scale-105 text-white rounded-full font-bold shadow transition flex items-center justify-center gap-2 text-xs sm:text-sm"
                        title="Generate WebRTC viewer link"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3 sm:h-4 sm:w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 10l4.553-2.276A1 1 0 0010 8.618v6.764a1 1 0 001.447.894L5 14"
                          />
                        </svg>
                        <span className="hidden sm:inline">WebRTC Link</span>
                        <span className="sm:hidden">WebRTC</span>
                      </button>
                    </>
                  )}
                  <button
                    onClick={endLiveStream}
                    className="px-4 sm:px-6 py-2 bg-gradient-to-r from-[#563D39] to-[#BC857D] hover:scale-105 text-white rounded-full font-bold shadow transition text-xs sm:text-sm"
                  >
                    {isStreaming && !isViewingStream
                      ? "End Stream"
                      : "Leave Stream"}
                  </button>
                </div>
              </div>
            </div>
          )}
        </section>

        {/* Comments/Chat Panel */}
        {(isStreaming || isViewingStream) && (
          <aside className="w-full lg:w-[350px] xl:w-[400px] flex flex-col bg-white rounded-xl sm:rounded-2xl shadow-lg border border-[#E0E0E0] max-h-[500px] sm:max-h-[600px] lg:max-h-[700px] min-h-[400px] sm:min-h-[500px] relative order-1 lg:order-2">
            <div className="p-3 sm:p-4 border-b border-[#E0E0E0] bg-gradient-to-r from-[#F9F9F9] to-[#E0E0E0] rounded-t-xl sm:rounded-t-2xl">
              <h3 className="font-bold text-[#563D39] text-base sm:text-lg">
                Comments
              </h3>
            </div>
            <div
              className="flex-1 overflow-y-auto p-3 sm:p-4 space-y-3 sm:space-y-4 thin-scrollbar pb-24 sm:pb-28 lg:pb-32 relative"
              id="comments-list"
              ref={commentsContainerRef}
              onScroll={handleScroll}
            >
              {/* Scroll to bottom button */}
              {showScrollToBottom && comments.length > 0 && (
                <button
                  onClick={scrollToBottom}
                  className="absolute bottom-20 right-3 sm:right-4 z-10 bg-gradient-to-r from-[#563D39] to-[#BC857D] text-white p-2 rounded-full shadow-lg hover:scale-110 transition-all duration-200"
                  title="Scroll to bottom"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3 sm:h-4 sm:w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 14l-7 7m0 0l-7-7m7 7V3"
                    />
                  </svg>
                </button>
              )}
              {comments.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-[#674941] opacity-60">
                  <span className="text-sm sm:text-base">No comments yet</span>
                </div>
              ) : (
                comments.map((comment, index) => (
                  <div
                    key={index}
                    className={`flex items-end gap-2 ${
                      comment.isOwnMessage ? "justify-end" : "justify-start"
                    }`}
                  >
                    {/* Avatar/Initials */}
                    {!comment.isOwnMessage && (
                      <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gradient-to-r from-[#563D39] to-[#BC857D] flex items-center justify-center text-white font-bold text-xs sm:text-sm flex-shrink-0">
                        {comment.username?.[0]?.toUpperCase() || "?"}
                      </div>
                    )}
                    <div
                      className={`max-w-[75%] px-3 sm:px-4 py-2 rounded-2xl shadow ${
                        comment.isOwnMessage
                          ? "bg-gradient-to-r from-[#F9F9F9] to-[#E0E0E0] text-[#563D39] ml-auto"
                          : "bg-[#F9F9F9] text-[#674941]"
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-semibold text-xs">
                          {comment.username}
                        </span>
                        {/* <span className="text-xs text-[#BC857D]">
                          {comment.timestamp instanceof Date
                            ? comment.timestamp.toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })
                            : new Date(comment.timestamp).toLocaleTimeString(
                                [],
                                {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                }
                              )}
                        </span> */}
                      </div>
                      <p className="text-xs sm:text-sm break-words">
                        {comment.message}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
            {/* Emoji Row */}
            <div className="absolute bottom-16 left-0 w-full px-3 sm:px-4 py-2 bg-gradient-to-r from-[#F9F9F9] to-[#E0E0E0] border-t border-[#E0E0E0]">
              <div className="flex gap-1 sm:gap-2 overflow-x-auto pb-2 emoji-scrollbar scrollbar-hide">
                {[
                  "💗",
                  "💸",
                  "🔥",
                  "👏🏻",
                  "😢",
                  "😍",
                  "😮",
                  "😂",
                  "🎉",
                  "✨",
                  "🌟",
                  "💫",
                  "💯",
                  "🙌",
                  "🤩",
                  "🥳",
                  "🎊",
                  "🏆",
                  "💪",
                  "👍",
                  "❤️",
                  "💖",
                  "💝",
                  "💕",
                  "💓",
                  "💞",
                  "💘",
                  "💗",
                  "💓",
                  "💖",
                ].map((emoji, index) => (
                  <button
                    key={index}
                    onClick={() => sendEmojiComment(emoji)}
                    className="text-lg sm:text-xl lg:text-2xl hover:scale-110 transition-transform duration-200 flex-shrink-0"
                  >
                    {emoji}
                  </button>
                ))}
              </div>
            </div>

            {/* Sticky Input */}
            <div className="absolute bottom-0 left-0 w-full p-3 sm:p-4 bg-gradient-to-t from-white/95 to-white/60 rounded-b-xl sm:rounded-b-2xl border-t border-[#E0E0E0]">
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  placeholder="Add a comment..."
                  className="flex-1 px-3 sm:px-4 py-2 border border-[#E0E0E0] rounded-full focus:outline-none focus:ring-2 focus:ring-[#674941] font-[Ubuntu,sans-serif] text-xs sm:text-sm shadow"
                  value={commentInput}
                  onChange={(e) => setCommentInput(e.target.value)}
                  onKeyPress={(e) =>
                    e.key === "Enter" && e.target.value.trim() && sendComment()
                  }
                />
                <button
                  onClick={sendComment}
                  disabled={!commentInput.trim()}
                  className={`px-3 sm:px-4 lg:px-5 py-2 rounded-full font-bold shadow transition flex items-center text-xs sm:text-sm ${
                    commentInput.trim()
                      ? "bg-gradient-to-r from-[#563D39] to-[#BC857D] hover:scale-105 text-white"
                      : "bg-gray-300 text-gray-400 cursor-not-allowed"
                  }`}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 sm:h-5 sm:w-5 mr-1"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11h2a1 1 0 001-1v-.571a1 1 0 01.725-.962l5-1.429a1 1 0 001.17-1.409l-7-14z" />
                  </svg>
                  <span className="hidden sm:inline">Send</span>
                  <span className="sm:hidden">→</span>
                </button>
              </div>
            </div>
          </aside>
        )}
      </main>

      <style jsx>{`
        .thin-scrollbar::-webkit-scrollbar {
          width: 4px;
        }
        .thin-scrollbar::-webkit-scrollbar-thumb {
          background: #e0e0e0;
          border-radius: 4px;
        }
        .thin-scrollbar::-webkit-scrollbar-track {
          background: #f9f9f9;
        }

        .emoji-scrollbar::-webkit-scrollbar {
          height: 3px;
        }
        .emoji-scrollbar::-webkit-scrollbar-thumb {
          background: #bc857d;
          border-radius: 2px;
        }
        .emoji-scrollbar::-webkit-scrollbar-track {
          background: #f0f0f0;
          border-radius: 2px;
        }

        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }

        /* Mobile-specific styles */
        @media (max-width: 640px) {
          .emoji-scrollbar::-webkit-scrollbar {
            height: 2px;
          }

          /* Improve touch targets on mobile */
          button {
            min-height: 44px;
            min-width: 44px;
          }
        }

        /* Tablet optimizations */
        @media (min-width: 641px) and (max-width: 1024px) {
          .emoji-scrollbar::-webkit-scrollbar {
            height: 4px;
          }
        }

        /* Desktop enhancements */
        @media (min-width: 1025px) {
          .emoji-scrollbar::-webkit-scrollbar {
            height: 6px;
          }
        }
      `}</style>

      {/* WebRTC Viewer Generator Modal */}
      {showWebRTCViewer && (
        <WebRTCViewerGenerator
          roomId={roomId}
          onClose={() => setShowWebRTCViewer(false)}
        />
      )}
    </div>
  );
};

export default LiveStreamApp;
