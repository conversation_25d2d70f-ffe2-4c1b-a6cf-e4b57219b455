{"name": "flowkar-admin", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "react-scripts start", "test": "react-scripts test", "eject": "react-scripts eject", "postinstall": "cp -r node_modules/@ffmpeg/core/dist/* public/"}, "dependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@headlessui/react": "^2.1.3", "@heroicons/react": "^2.1.5", "@material-tailwind/react": "^2.1.10", "@mui/icons-material": "^5.16.1", "@mui/lab": "^5.0.0-alpha.172", "@mui/material": "^5.16.7", "@mui/styled-engine-sc": "^5.14.9", "@mui/styles": "^5.16.5", "@mui/x-date-pickers": "^7.18.0", "@reduxjs/toolkit": "^2.2.7", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.74.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "animate.css": "^4.1.1", "axios": "^1.4.0", "date-fns": "^4.1.0", "daterangepicker": "^3.1.0", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "flowbite": "^2.4.1", "flowbite-react": "^0.10.1", "formik": "^2.4.6", "framer-motion": "^12.5.0", "jquery": "^3.7.1", "lodash": "^4.17.21", "lucide-react": "^0.479.0", "moment": "^2.30.1", "react": "^18.3.1", "react-audio-voice-recorder": "^2.2.0", "react-date-range": "^2.0.1", "react-datepicker": "^7.3.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-icons": "^5.2.1", "react-onesignal": "^3.2.2", "react-player": "^2.16.0", "react-redux": "^9.1.2", "react-router-dom": "^6.30.1", "react-scripts": "^5.0.1", "react-slick": "^0.30.2", "react-spinners": "^0.17.0", "react-toastify": "^9.1.3", "react-use": "^17.6.0", "recharts": "^2.12.7", "simple-peer": "^9.11.1", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "tw-elements-react": "^1.0.0-alpha-end", "web-vitals": "^2.1.4", "webrtc-adapter": "^9.0.1", "yup": "^0.29.3"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}